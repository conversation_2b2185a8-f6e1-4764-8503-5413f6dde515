package com.maguo.loan.cash.flow.entrance.common.service;

import com.maguo.loan.cash.flow.entity.common.ProjectInfo;
import com.maguo.loan.cash.flow.repository.ProjectInfoRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 项目相关服务
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 14:11
 */
@Service
public class ProjectInfoService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectInfoService.class);

    @Autowired
    private ProjectInfoRepository projectInfoRepository;


    /**
     * 查询项目
     */
    public ProjectInfo queryProject(String projectId) {
        return projectInfoRepository.findByProjectId(projectId);
    }


}
